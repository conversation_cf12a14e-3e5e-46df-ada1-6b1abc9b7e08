import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { isDemoMode } from '@/app/config/demoMode';
import { UserService } from '@/lib/services/userService';
import { prisma } from '@/lib/prisma';

// Cancel pending invitation
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { invitationId } = await request.json();

    if (!invitationId) {
      return NextResponse.json(
        { error: 'Invitation ID is required' },
        { status: 400 }
      );
    }

    // Get user from database to get the internal user ID
    const dbUser = await UserService.findBySupabaseId(user.id);
    if (!dbUser) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    const demoMode = isDemoMode();

    // Delete pending invitation (only if it belongs to the current user)
    const result = await prisma.pendingInvitation.deleteMany({
      where: {
        id: invitationId,
        senderId: dbUser.id
      }
    });

    if (result.count > 0) {
      console.log(`[${demoMode ? 'DEMO' : 'PROD'} MODE] Invitation ${invitationId} cancelled by user ${dbUser.id}`);

      return NextResponse.json({
        success: true,
        message: `Invitation cancelled successfully${demoMode ? ' (Demo Mode)' : ''}`,
        demoMode
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to cancel invitation. It may not exist or already be processed.' },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('Error cancelling invitation:', error);
    return NextResponse.json(
      {
        error: 'Failed to cancel invitation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
