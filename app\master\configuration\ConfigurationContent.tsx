'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/app/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Input from '@/app/components/Input';
import BrokerCard from '@/app/components/BrokerCard';
import { motion } from 'framer-motion';
import { GlowingBorder } from '@/app/components/ui/animated-border';
import { isDemoMode } from '@/app/config/demoMode';
import {
  Users,
  Mail,
  CheckCircle,
  AlertCircle,
  Settings,
  UserPlus,
  Shield,
  Key,
  Link as LinkIcon,
  Unlink,
  Building2,
  TrendingUp,
  Loader2,
  X,
  Crown,
  Star,
  Clock
} from 'lucide-react';

// Broker types and data
interface Broker {
  id: string;
  name: string;
  logo: string;
  description: string;
  status: 'available' | 'coming_soon' | 'maintenance';
}

interface BrokerConnection {
  brokerId: string;
  brokerName: string;
  userId: string;
  accountId: string;
  connectedAt: string;
  status: 'active' | 'inactive' | 'error';
}

interface ChildUser {
  id: string;
  email: string;
  name: string;
  connectedAt: string;
  status: 'active' | 'pending' | 'inactive';
  zerodhaUserId?: string;
}



export default function ConfigurationContent() {
  const [childEmail, setChildEmail] = useState('');
  const [isInviting, setIsInviting] = useState(false);
  const [inviteSuccess, setInviteSuccess] = useState(false);
  const [inviteError, setInviteError] = useState('');
  const [childUsers, setChildUsers] = useState<ChildUser[]>([]);
  const [pendingInvitations, setPendingInvitations] = useState<PendingInvitation[]>([]);
  const [loadingChildren, setLoadingChildren] = useState(false);
  const [brokers, setBrokers] = useState<Broker[]>([]);
  const [connectedBroker, setConnectedBroker] = useState<BrokerConnection | null>(null);
  const [loadingBrokers, setLoadingBrokers] = useState(false);
  const [connectingBroker, setConnectingBroker] = useState<string | null>(null);
  const [demoMode, setDemoMode] = useState(false);

  const { user, inviteChild, getChildUsers } = useAuth();

  // Load initial data
  useEffect(() => {
    setDemoMode(isDemoMode());
    loadBrokers();
    loadBrokerStatus();
    loadChildUsers();
  }, []);

  const loadBrokers = async () => {
    setLoadingBrokers(true);
    try {
      const response = await fetch('/api/broker/list');
      if (response.ok) {
        const data = await response.json();
        setBrokers(data.brokers);
        setDemoMode(data.demoMode);
      }
    } catch (error) {
      console.error('Error loading brokers:', error);
    } finally {
      setLoadingBrokers(false);
    }
  };

  const loadBrokerStatus = async () => {
    try {
      const response = await fetch('/api/broker/status');
      if (response.ok) {
        const data = await response.json();
        setConnectedBroker(data.connection);
      }
    } catch (error) {
      console.error('Error loading broker status:', error);
    }
  };

  const loadChildUsers = async () => {
    setLoadingChildren(true);
    try {
      const response = await fetch('/api/child/list');
      if (response.ok) {
        const data = await response.json();
        setChildUsers(data.childUsers || []);
        setPendingInvitations(data.pendingInvitations || []);
      }
    } catch (error) {
      console.error('Error loading child users:', error);
    } finally {
      setLoadingChildren(false);
    }
  };

  const handleConnectBroker = async (brokerId: string) => {
    setConnectingBroker(brokerId);
    try {
      const response = await fetch('/api/broker/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ brokerId }),
      });

      const data = await response.json();
      if (data.success) {
        setConnectedBroker(data.connection);
        console.log('Successfully connected to broker:', data.connection);
        // Don't reload broker status since we already have the fresh connection data
      } else if (data.authUrl) {
        // Redirect to broker OAuth
        window.location.href = data.authUrl;
      } else {
        console.error('Failed to connect to broker:', data.error);
      }
    } catch (error) {
      console.error('Error connecting to broker:', error);
    } finally {
      setConnectingBroker(null);
    }
  };

  const handleDisconnectBroker = async () => {
    try {
      const response = await fetch('/api/broker/disconnect', {
        method: 'DELETE',
      });

      const data = await response.json();
      if (data.success) {
        setConnectedBroker(null);
        console.log('Successfully disconnected from broker');
        // Don't reload broker status since we already know the connection is null
      }
    } catch (error) {
      console.error('Error disconnecting from broker:', error);
    }
  };

  const handleInviteChild = async (e: React.FormEvent) => {
    e.preventDefault();
    setInviteSuccess(false);
    setInviteError('');
    setIsInviting(true);

    try {
      const response = await fetch('/api/child/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ childEmail }),
      });

      const data = await response.json();
      if (data.success) {
        setInviteSuccess(true);
        setChildEmail('');
        await loadChildUsers();
      } else {
        setInviteError(data.error || 'Failed to send invitation');
      }
    } catch (error) {
      console.error('Invitation error:', error);
      setInviteError('Failed to send invitation. Please try again.');
    } finally {
      setIsInviting(false);
    }
  };

  const handleRemoveChild = async (childId: string) => {
    try {
      const response = await fetch('/api/child/remove', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ childId }),
      });

      const data = await response.json();
      if (data.success) {
        await loadChildUsers();
      }
    } catch (error) {
      console.error('Error removing child user:', error);
    }
  };

  const handleCancelInvitation = async (invitationId: string) => {
    try {
      const response = await fetch(`/api/child/cancel-invitation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ invitationId }),
      });

      if (response.ok) {
        await loadChildUsers(); // Reload to update the lists
      } else {
        console.error('Failed to cancel invitation');
      }
    } catch (error) {
      console.error('Error canceling invitation:', error);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-background">
      {/* Animated background gradient */}
      <div className="fixed inset-0 bg-gradient-to-br from-blue-50/50 via-background to-purple-50/50 dark:from-blue-950/10 dark:via-background dark:to-purple-950/10" />

      {/* Animated gradient overlay */}
      <motion.div
        className="fixed inset-0 opacity-30 dark:opacity-20 pointer-events-none"
        animate={{
          background: [
            "radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 80% 50%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 50% 80%, rgba(6, 182, 212, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)"
          ]
        }}
        transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
      />

      <main className="flex-grow relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <motion.div
            className="flex items-center justify-between mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.h1
              className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl flex items-center gap-3"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Settings className="w-8 h-8 text-blue-600" />
              Configuration
              {demoMode && (
                <Badge className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-purple-300/50 text-purple-700 dark:text-purple-400">
                  Demo Mode
                </Badge>
              )}
            </motion.h1>
          </motion.div>

          {/* Broker Selection Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mb-8"
          >
            <GlowingBorder className="h-full" glowColor="blue">
              <Card className="border-0 bg-background/50 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <motion.div
                      className="p-3 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-lg"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Building2 className="w-6 h-6 text-blue-600" />
                    </motion.div>
                    <div>
                      <CardTitle className="text-xl font-semibold text-foreground">Broker Selection</CardTitle>
                      <CardDescription className="text-muted-foreground">
                        Connect to your preferred broker to start trading
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {loadingBrokers ? (
                    <div className="flex justify-center items-center py-8">
                      <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
                      <span className="ml-2 text-muted-foreground">Loading brokers...</span>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {brokers.map((broker, index) => (
                        <motion.div
                          key={broker.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.4, delay: index * 0.1 }}
                        >
                          <BrokerCard
                            broker={broker}
                            isConnected={connectedBroker?.brokerId === broker.id}
                            isConnecting={connectingBroker === broker.id}
                            onConnect={handleConnectBroker}
                            onDisconnect={handleDisconnectBroker}
                            demoMode={demoMode}
                          />
                        </motion.div>
                      ))}
                    </div>
                  )}

                  {/* Connected Broker Status */}
                  {connectedBroker && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mt-6 p-4 bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-200 rounded-lg"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-green-700 dark:text-green-400">
                          <CheckCircle className="w-5 h-5 mr-2" />
                          <div>
                            <p className="font-medium">Connected to {connectedBroker.brokerName}</p>
                            <p className="text-sm opacity-75">Account: {connectedBroker.accountId}</p>
                          </div>
                        </div>
                        <Badge className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-300/50 text-green-700 dark:text-green-400">
                          Active
                        </Badge>
                      </div>
                    </motion.div>
                  )}
                </CardContent>
              </Card>
            </GlowingBorder>
          </motion.div>

          {/* Master Account Settings */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="mb-8"
          >
            <GlowingBorder className="h-full" glowColor="purple">
              <Card className="border-0 bg-background/50 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <motion.div
                      className="p-3 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Crown className="w-6 h-6 text-purple-600" />
                    </motion.div>
                    <div>
                      <CardTitle className="text-xl font-semibold text-foreground">Master Account Settings</CardTitle>
                      <CardDescription className="text-muted-foreground">
                        Your account information and trading preferences
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-foreground flex items-center gap-2">
                          <Mail className="w-4 h-4" />
                          Account Email
                        </label>
                        <p className="text-sm text-muted-foreground mt-1 font-mono bg-muted/50 px-2 py-1 rounded">
                          {user?.email || 'Not available'}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-foreground flex items-center gap-2">
                          <Shield className="w-4 h-4" />
                          Account Type
                        </label>
                        <div className="mt-1">
                          <Badge className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-purple-300/50 text-purple-700 dark:text-purple-400">
                            <Crown className="w-3 h-3 mr-1" />
                            Master Trader
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-foreground flex items-center gap-2">
                          <Building2 className="w-4 h-4" />
                          Connected Broker
                        </label>
                        <div className="mt-1">
                          {connectedBroker ? (
                            <Badge className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-300/50 text-green-700 dark:text-green-400">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              {connectedBroker.brokerName}
                            </Badge>
                          ) : (
                            <Badge className="bg-gradient-to-r from-red-500/20 to-pink-500/20 border-red-300/50 text-red-700 dark:text-red-400">
                              <AlertCircle className="w-3 h-3 mr-1" />
                              No Broker Connected
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-foreground flex items-center gap-2">
                          <Users className="w-4 h-4" />
                          Child Users
                        </label>
                        <p className="text-sm text-muted-foreground mt-1">
                          {childUsers.length} connected
                        </p>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-foreground flex items-center gap-2">
                          <TrendingUp className="w-4 h-4" />
                          Trading Status
                        </label>
                        <div className="mt-1">
                          {connectedBroker ? (
                            <Badge className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-300/50 text-green-700 dark:text-green-400">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Ready to Trade
                            </Badge>
                          ) : (
                            <Badge className="bg-gradient-to-r from-amber-500/20 to-orange-500/20 border-amber-300/50 text-amber-700 dark:text-amber-400">
                              <AlertCircle className="w-3 h-3 mr-1" />
                              Setup Required
                            </Badge>
                          )}
                        </div>
                      </div>
                      {user?.subscription && (
                        <div>
                          <label className="text-sm font-medium text-foreground flex items-center gap-2">
                            <Star className="w-4 h-4" />
                            Subscription
                          </label>
                          <div className="mt-1">
                            <Badge className="bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border-blue-300/50 text-blue-700 dark:text-blue-400">
                              {user.subscription.plan.name}
                            </Badge>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </GlowingBorder>
          </motion.div>

          {/* Child Users Management */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="mb-8"
          >
            <GlowingBorder className="h-full" glowColor="cyan">
              <Card className="border-0 bg-background/50 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <motion.div
                        className="p-3 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-lg"
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Users className="w-6 h-6 text-cyan-600" />
                      </motion.div>
                      <div>
                        <CardTitle className="text-xl font-semibold text-foreground">Child Users Management</CardTitle>
                        <CardDescription className="text-muted-foreground">
                          Invite and manage users who will copy your trades
                        </CardDescription>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-2 justify-end">
                        <Badge className="bg-gradient-to-r from-cyan-500/20 to-blue-500/20 border-cyan-300/50 text-cyan-700 dark:text-cyan-400">
                          {childUsers.length} Connected
                        </Badge>
                        {pendingInvitations.length > 0 && (
                          <Badge className="bg-gradient-to-r from-amber-500/20 to-orange-500/20 border-amber-300/50 text-amber-700 dark:text-amber-400">
                            {pendingInvitations.length} Pending
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Subscription-based invitation limits */}
                  {!connectedBroker && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mb-6 p-4 bg-gradient-to-r from-amber-500/10 to-orange-500/10 border border-amber-200 rounded-lg"
                    >
                      <div className="flex items-center text-amber-700 dark:text-amber-400">
                        <AlertCircle className="w-5 h-5 mr-2" />
                        <div>
                          <p className="font-medium">Broker Connection Required</p>
                          <p className="text-sm opacity-75">Connect to a broker above before inviting child users.</p>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  {/* Invite Form */}
                  <form onSubmit={handleInviteChild} className="mb-6">
                    <div className="flex flex-col sm:flex-row gap-4">
                      <div className="flex-grow">
                        <Input
                          type="email"
                          placeholder="Enter child user email address"
                          value={childEmail}
                          onChange={(e) => setChildEmail(e.target.value)}
                          required
                          disabled={!connectedBroker || isInviting}
                          fullWidth
                          className="bg-background/50 border-2 border-transparent bg-gradient-to-r from-cyan-500/10 via-blue-500/10 to-purple-500/10 focus:from-cyan-500/20 focus:via-blue-500/20 focus:to-purple-500/20 transition-all duration-300"
                        />
                      </div>
                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Button
                          type="submit"
                          disabled={isInviting || !connectedBroker}
                          className="bg-gradient-to-r from-cyan-600 to-blue-600 hover:from-cyan-700 hover:to-blue-700 transition-all duration-300"
                        >
                          {isInviting ? (
                            <div className="flex items-center">
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                              Sending...
                            </div>
                          ) : (
                            <div className="flex items-center">
                              <UserPlus className="w-4 h-4 mr-2" />
                              Send Invitation
                            </div>
                          )}
                        </Button>
                      </motion.div>
                    </div>

                    {inviteSuccess && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-4 p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-200 rounded-lg"
                      >
                        <div className="flex items-center text-green-700 dark:text-green-400">
                          <CheckCircle className="w-4 h-4 mr-2" />
                          <div>
                            <p className="font-medium">Invitation sent successfully!</p>
                            <p className="text-sm opacity-75">The user will receive an email with instructions to connect.</p>
                          </div>
                        </div>
                      </motion.div>
                    )}

                    {inviteError && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-4 p-3 bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-200 rounded-lg"
                      >
                        <div className="flex items-center text-red-700 dark:text-red-400">
                          <AlertCircle className="w-4 h-4 mr-2" />
                          <div>
                            <p className="font-medium">Invitation failed</p>
                            <p className="text-sm opacity-75">{inviteError}</p>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </form>

                  {/* Child Users List */}
                  <div className="mt-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-foreground flex items-center">
                        <Users className="w-5 h-5 mr-2 text-cyan-600" />
                        Connected Child Users
                      </h3>
                      {childUsers.length > 0 && (
                        <Badge className="bg-gradient-to-r from-cyan-500/20 to-blue-500/20 border-cyan-300/50 text-cyan-700 dark:text-cyan-400">
                          {childUsers.length} Active
                        </Badge>
                      )}
                    </div>

                    {loadingChildren ? (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="p-8 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-lg text-center"
                      >
                        <div className="flex justify-center items-center text-muted-foreground">
                          <Loader2 className="w-6 h-6 animate-spin text-cyan-600 mr-2" />
                          Loading child users...
                        </div>
                      </motion.div>
                    ) : childUsers.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {childUsers.map((child, index) => (
                          <motion.div
                            key={child.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.4, delay: index * 0.1 }}
                          >
                            <GlowingBorder glowColor={index % 3 === 0 ? "cyan" : index % 3 === 1 ? "blue" : "purple"}>
                              <Card className="border-0 bg-background/50 backdrop-blur-sm h-full">
                                <CardContent className="p-4">
                                  <div className="flex items-start justify-between">
                                    <div className="space-y-2 flex-grow">
                                      <div className="flex items-center gap-2">
                                        <p className="font-medium text-foreground">{child.name || child.email}</p>
                                        <Badge
                                          className={`text-xs ${
                                            child.status === 'active'
                                              ? 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-300/50 text-green-700 dark:text-green-400'
                                              : 'bg-gradient-to-r from-amber-500/20 to-orange-500/20 border-amber-300/50 text-amber-700 dark:text-amber-400'
                                          }`}
                                        >
                                          {child.status === 'active' ? (
                                            <>
                                              <CheckCircle className="w-3 h-3 mr-1" />
                                              Active
                                            </>
                                          ) : (
                                            <>
                                              <AlertCircle className="w-3 h-3 mr-1" />
                                              Pending
                                            </>
                                          )}
                                        </Badge>
                                      </div>
                                      <p className="text-sm text-muted-foreground font-mono bg-muted/50 px-2 py-1 rounded">
                                        {child.email}
                                      </p>
                                      {child.zerodhaUserId && (
                                        <p className="text-xs text-muted-foreground">
                                          Broker ID: {child.zerodhaUserId}
                                        </p>
                                      )}
                                      <p className="text-xs text-muted-foreground">
                                        Connected: {new Date(child.connectedAt).toLocaleDateString()}
                                      </p>
                                    </div>
                                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handleRemoveChild(child.id)}
                                        className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
                                      >
                                        <X className="w-4 h-4" />
                                      </Button>
                                    </motion.div>
                                  </div>
                                </CardContent>
                              </Card>
                            </GlowingBorder>
                          </motion.div>
                        ))}
                      </div>
                    ) : (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="p-8 bg-gradient-to-r from-gray-500/10 to-slate-500/10 rounded-lg text-center"
                      >
                        <div className="text-muted-foreground">
                          <Users className="w-12 h-12 mx-auto mb-3 opacity-50" />
                          <p className="text-lg font-medium mb-1">No child users connected yet</p>
                          <p className="text-sm">Send an invitation above to get started with copy trading!</p>
                        </div>
                      </motion.div>
                    )}
                  </div>

                  {/* Pending Invitations Section */}
                  {pendingInvitations.length > 0 && (
                    <div className="mt-6 pt-4 border-t border-border/50">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="text-sm font-medium text-foreground flex items-center">
                          <Clock className="w-4 h-4 mr-2 text-amber-600" />
                          Pending Invitations ({pendingInvitations.length})
                        </h3>
                      </div>

                      <div className="space-y-2">
                        {pendingInvitations.map((invitation, index) => (
                          <motion.div
                            key={invitation.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.3, delay: index * 0.05 }}
                            className="flex items-center justify-between p-3 bg-gradient-to-r from-amber-500/5 to-orange-500/5 border border-amber-200/30 rounded-lg hover:from-amber-500/10 hover:to-orange-500/10 transition-all duration-200"
                          >
                            <div className="flex items-center gap-3 flex-grow">
                              <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
                              <div className="flex items-center gap-2">
                                <span className="font-medium text-foreground">{invitation.childEmail}</span>
                                <Badge className="text-xs bg-gradient-to-r from-amber-500/20 to-orange-500/20 border-amber-300/50 text-amber-700 dark:text-amber-400">
                                  Pending
                                </Badge>
                              </div>
                              <div className="hidden sm:flex items-center gap-4 text-xs text-muted-foreground">
                                <span>Sent: {new Date(invitation.sentAt).toLocaleDateString()}</span>
                                <span>Expires: {new Date(invitation.expiresAt).toLocaleDateString()}</span>
                              </div>
                            </div>
                            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleCancelInvitation(invitation.id)}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20 h-8 w-8 p-0"
                              >
                                <X className="w-4 h-4" />
                              </Button>
                            </motion.div>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </GlowingBorder>
          </motion.div>
        </div>
      </main>
    </div>
  );
}
