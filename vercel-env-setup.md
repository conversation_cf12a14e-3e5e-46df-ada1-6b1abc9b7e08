# Vercel Environment Variables Setup

## Critical Environment Variables for Email Invitation Fix

To fix the "invalid or expired token" error on Vercel, you need to set these environment variables in your Vercel dashboard:

### Required Environment Variables

1. **JWT_SECRET** (CRITICAL - This is causing the token validation failure)
   ```
   JWT_SECRET=your-secret-key-for-jwt-tokens
   ```

2. **Email Configuration**
   ```
   EMAIL_HOST=smtp.gmail.com
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=hmyk vycy fyhu xakp
   ```

3. **Zerodha API**
   ```
   NEXT_PUBLIC_ZERODHA_API_KEY=lka6dbqhu4u0ivqe
   ZERODHA_API_SECRET=5351a54cvbjral2bipk554fkcp6d1set
   ```

4. **Database**
   ```
   DATABASE_URL=postgresql://postgres.zcywnumitabjugszokmy:<EMAIL>:6543/postgres?pgbouncer=true&schema=public
   DIRECT_URL=postgresql://postgres.zcywnumitabjugszokmy:<EMAIL>:5432/postgres?schema=public
   ```

5. **Supabase**
   ```
   NEXT_PUBLIC_SUPABASE_URL=https://zcywnumitabjugszokmy.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpjeXdudW1pdGFianVnc3pva215Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMDUyNTgsImV4cCI6MjA2MzU4MTI1OH0.h2gIZopIA4p5zpn3I1zEH8s4t9VorUbIendMUKdnrLU
   ```

6. **Google OAuth**
   ```
   GOOGLE_CLIENT_ID=575583007132-8s4ivseknldbe1pk2fbomahpu0n83qn6.apps.googleusercontent.com
   GOOGLE_CLIENT_SECRET=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
   SUPBASE_CALLBACK_URL=https://zcywnumitabjugszokmy.supabase.co/auth/v1/callback
   ```

## How to Set Environment Variables on Vercel

1. Go to your Vercel dashboard
2. Select your CopyTrade project
3. Go to Settings → Environment Variables
4. Add each variable above with the exact values
5. Make sure to set them for all environments (Production, Preview, Development)
6. Redeploy your application after adding the variables

## What Was Fixed

1. **Removed client-side JWT verification** - JWT secrets are not available on the client-side in production
2. **Added proper JWT secret validation** - All server-side endpoints now properly check for JWT_SECRET
3. **Improved error handling** - Better error messages when JWT_SECRET is missing
4. **Fixed URL construction** - Better handling of Vercel URLs for internal API calls

## Testing the Fix

After setting up the environment variables and redeploying:

1. Send an invitation email from your deployed app
2. Click the invitation link in the email
3. The token validation should now work correctly
4. You should be able to accept the invitation without the "invalid or expired token" error

## Important Notes

- The JWT_SECRET must be exactly the same as your local environment
- Make sure to redeploy after adding environment variables
- Check Vercel function logs if you still encounter issues
