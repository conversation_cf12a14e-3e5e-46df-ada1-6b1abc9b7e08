import { createClient } from '@/utils/supabase/server'
import { UserService } from './user.service'
import { isDemoMode, generateMockData, logDemoAction } from '../utils/demo-mode'
import type { AuthUser } from '../types'

export class AuthService {
  // Get current user from Supabase session
  static async getCurrentUser(): Promise<AuthUser | null> {
    try {
      if (isDemoMode()) {
        // Return demo user if in demo mode
        const demoUser = generateMockData('user')
        logDemoAction('Get Current User (Demo)', demoUser)
        return demoUser
      }

      const supabase = await createClient()
      const { data: { user }, error } = await supabase.auth.getUser()

      if (error || !user) {
        return null
      }

      // Get user from database
      const dbUser = await UserService.findBySupabaseId(user.id)
      
      if (!dbUser) {
        // Create user if doesn't exist
        const newUser = await UserService.createOrGetSupabaseUser({
          id: user.id,
          email: user.email!,
          user_metadata: user.user_metadata,
        })
        return UserService.toAuthUser(newUser)
      }

      return UserService.toAuthUser(dbUser)
    } catch (error) {
      console.error('Error getting current user:', error)
      return null
    }
  }

  // Sign in with email and password
  static async signIn(email: string, password: string): Promise<{ user: AuthUser | null; error: string | null }> {
    try {
      if (isDemoMode()) {
        // Mock sign in for demo mode
        const demoUser = generateMockData('user')
        demoUser.email = email
        logDemoAction('Sign In (Demo)', { email })
        return { user: demoUser, error: null }
      }

      const supabase = await createClient()
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        return { user: null, error: error.message }
      }

      if (!data.user) {
        return { user: null, error: 'No user returned' }
      }

      // Get or create user in database
      const dbUser = await UserService.createOrGetSupabaseUser({
        id: data.user.id,
        email: data.user.email!,
        user_metadata: data.user.user_metadata,
      })

      return { user: UserService.toAuthUser(dbUser), error: null }
    } catch (error) {
      return { user: null, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  // Sign up with email and password
  static async signUp(email: string, password: string, metadata?: any): Promise<{ user: AuthUser | null; error: string | null }> {
    try {
      if (isDemoMode()) {
        // Mock sign up for demo mode
        const demoUser = generateMockData('user')
        demoUser.email = email
        demoUser.name = metadata?.name
        logDemoAction('Sign Up (Demo)', { email, metadata })
        return { user: demoUser, error: null }
      }

      const supabase = await createClient()
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
        },
      })

      if (error) {
        return { user: null, error: error.message }
      }

      if (!data.user) {
        return { user: null, error: 'No user returned' }
      }

      // Create user in database
      const dbUser = await UserService.createOrGetSupabaseUser({
        id: data.user.id,
        email: data.user.email!,
        user_metadata: { ...data.user.user_metadata, ...metadata },
      })

      return { user: UserService.toAuthUser(dbUser), error: null }
    } catch (error) {
      return { user: null, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  // Sign out
  static async signOut(): Promise<{ error: string | null }> {
    try {
      if (isDemoMode()) {
        logDemoAction('Sign Out (Demo)')
        return { error: null }
      }

      const supabase = await createClient()
      const { error } = await supabase.auth.signOut()

      if (error) {
        return { error: error.message }
      }

      return { error: null }
    } catch (error) {
      return { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  // Sign in with OAuth provider
  static async signInWithOAuth(provider: 'google' | 'github'): Promise<{ url: string | null; error: string | null }> {
    try {
      if (isDemoMode()) {
        logDemoAction('OAuth Sign In (Demo)', { provider })
        // Return demo success
        return { url: '/dashboard', error: null }
      }

      const supabase = await createClient()
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
        },
      })

      if (error) {
        return { url: null, error: error.message }
      }

      return { url: data.url, error: null }
    } catch (error) {
      return { url: null, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  // Reset password
  static async resetPassword(email: string): Promise<{ error: string | null }> {
    try {
      if (isDemoMode()) {
        logDemoAction('Reset Password (Demo)', { email })
        return { error: null }
      }

      const supabase = await createClient()
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/reset-password`,
      })

      if (error) {
        return { error: error.message }
      }

      return { error: null }
    } catch (error) {
      return { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  // Update password
  static async updatePassword(password: string): Promise<{ error: string | null }> {
    try {
      if (isDemoMode()) {
        logDemoAction('Update Password (Demo)')
        return { error: null }
      }

      const supabase = await createClient()
      const { error } = await supabase.auth.updateUser({ password })

      if (error) {
        return { error: error.message }
      }

      return { error: null }
    } catch (error) {
      return { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  // Verify session
  static async verifySession(): Promise<boolean> {
    try {
      if (isDemoMode()) {
        return true
      }

      const supabase = await createClient()
      const { data: { session } } = await supabase.auth.getSession()
      
      return !!session
    } catch (error) {
      console.error('Error verifying session:', error)
      return false
    }
  }

  // Refresh session
  static async refreshSession(): Promise<{ error: string | null }> {
    try {
      if (isDemoMode()) {
        return { error: null }
      }

      const supabase = await createClient()
      const { error } = await supabase.auth.refreshSession()

      if (error) {
        return { error: error.message }
      }

      return { error: null }
    } catch (error) {
      return { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}
