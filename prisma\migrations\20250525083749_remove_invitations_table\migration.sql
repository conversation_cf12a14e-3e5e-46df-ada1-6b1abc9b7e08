/*
  Warnings:

  - The values [INVITATION_SENT,INVITATION_ACCEPTED] on the enum `AuditAction` will be removed. If these variants are still used in the database, this will fail.
  - The values [INVITATION_SENT,INVITATION_ACCEPTED] on the enum `NotificationType` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the `invitations` table. If the table is not empty, all the data it contains will be lost.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "AuditAction_new" AS ENUM ('USER_CREATED', 'USER_UPDATED', 'USER_DELETED', 'TRADE_PLACED', 'TRADE_COPIED', 'LOGIN', 'LOGOUT', '<PERSON>ER<PERSON>HA_CONNECTED', 'ZERODHA_DISCONNECTED');
ALTER TABLE "audit_logs" ALTER COLUMN "action" TYPE "AuditAction_new" USING ("action"::text::"AuditAction_new");
ALTER TYPE "AuditAction" RENAME TO "AuditAction_old";
ALTER TYPE "AuditAction_new" RENAME TO "AuditAction";
DROP TYPE "AuditAction_old";
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "NotificationType_new" AS ENUM ('TRADE_COPIED', 'SYSTEM_ALERT', 'ERROR');
ALTER TABLE "notifications" ALTER COLUMN "type" TYPE "NotificationType_new" USING ("type"::text::"NotificationType_new");
ALTER TYPE "NotificationType" RENAME TO "NotificationType_old";
ALTER TYPE "NotificationType_new" RENAME TO "NotificationType";
DROP TYPE "NotificationType_old";
COMMIT;

-- DropForeignKey
ALTER TABLE "invitations" DROP CONSTRAINT "invitations_receiverId_fkey";

-- DropForeignKey
ALTER TABLE "invitations" DROP CONSTRAINT "invitations_senderId_fkey";

-- DropTable
DROP TABLE "invitations";

-- DropEnum
DROP TYPE "InvitationStatus";
