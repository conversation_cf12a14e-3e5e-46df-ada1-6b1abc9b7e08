import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { isDemoMode } from '@/app/config/demoMode';
import { UserRole } from '@prisma/client';
import { validateInvitationToken, type InvitationTokenData } from '@/lib/utils/jwt';

export async function POST(request: NextRequest) {
  try {
    const { token, childUserData } = await request.json();

    if (!token || !childUserData) {
      return NextResponse.json(
        { error: 'Token and child user data are required' },
        { status: 400 }
      );
    }

    // Validate the invitation token directly (no internal API call)
    console.log('Starting token validation for invitation acceptance...');
    const validationResult = validateInvitationToken(token);

    if (!validationResult.valid) {
      console.error('Token validation failed:', validationResult.error);
      return NextResponse.json(
        { error: validationResult.error || 'Invalid invitation token' },
        { status: 400 }
      );
    }

    const invitationData: InvitationTokenData = validationResult.invitationData!;
    console.log('Token validation successful for:', invitationData.childEmail);

    // Validate that the child email matches the invitation
    if (invitationData.childEmail !== childUserData.email) {
      return NextResponse.json(
        { error: 'Child email does not match invitation' },
        { status: 400 }
      );
    }

    const demoMode = isDemoMode();
    console.log(`[${demoMode ? 'DEMO' : 'PROD'} MODE] Processing invitation acceptance for child: ${childUserData.email}`);

    try {
      // Check if master user exists
      console.log('Looking up master user with ID:', invitationData.masterId);
      const masterUser = await prisma.user.findUnique({
        where: { id: invitationData.masterId },
        include: { zerodhaCredentials: true }
      });

      if (!masterUser) {
        console.error('Master user not found with ID:', invitationData.masterId);
        return NextResponse.json(
          { error: 'Master user not found' },
          { status: 404 }
        );
      }

      console.log('Master user found:', masterUser.email);

      // Check if child user already exists
      console.log('Checking if child user already exists:', childUserData.email);
      let childUser;
      const existingChild = await prisma.user.findUnique({
        where: { email: childUserData.email },
        include: { zerodhaCredentials: true }
      });

      if (existingChild) {
        console.log('Updating existing child user:', childUserData.email);
        // Update existing child user and their Zerodha credentials
        childUser = await prisma.user.update({
          where: { email: childUserData.email },
          data: {
            name: childUserData.name,
            role: UserRole.CHILD,
            zerodhaCredentials: {
              upsert: {
                create: {
                  zerodhaUserId: childUserData.zerodhaUserId,
                  accessToken: childUserData.zerodhaAccessToken,
                  refreshToken: childUserData.zerodhaRefreshToken,
                  isConnected: true,
                },
                update: {
                  zerodhaUserId: childUserData.zerodhaUserId,
                  accessToken: childUserData.zerodhaAccessToken,
                  refreshToken: childUserData.zerodhaRefreshToken,
                  isConnected: true,
                }
              }
            }
          },
          include: { zerodhaCredentials: true }
        });
        console.log('Child user updated successfully:', childUser.id);
      } else {
        console.log('Creating new child user:', childUserData.email);
        // Create new child user with Zerodha credentials
        childUser = await prisma.user.create({
          data: {
            email: childUserData.email,
            name: childUserData.name,
            role: UserRole.CHILD,
            zerodhaCredentials: {
              create: {
                zerodhaUserId: childUserData.zerodhaUserId,
                accessToken: childUserData.zerodhaAccessToken,
                refreshToken: childUserData.zerodhaRefreshToken,
                isConnected: true,
              }
            }
          },
          include: { zerodhaCredentials: true }
        });
        console.log('Child user created successfully:', childUser.id);
      }

      // Create or update master-child relationship
      console.log('Setting up master-child relationship...');
      const existingRelation = await prisma.masterChildRelationship.findUnique({
        where: {
          masterId_childId: {
            masterId: invitationData.masterId,
            childId: childUser.id
          }
        }
      });

      if (existingRelation) {
        console.log('Updating existing master-child relationship');
        // Update existing relationship
        await prisma.masterChildRelationship.update({
          where: {
            masterId_childId: {
              masterId: invitationData.masterId,
              childId: childUser.id
            }
          },
          data: {
            isActive: true,
            connectedAt: new Date(),
          }
        });
        console.log('Master-child relationship updated successfully');
      } else {
        console.log('Creating new master-child relationship');
        // Create new relationship
        await prisma.masterChildRelationship.create({
          data: {
            masterId: invitationData.masterId,
            childId: childUser.id,
            isActive: true,
            connectedAt: new Date(),
          }
        });
        console.log('Master-child relationship created successfully');
      }

      // Remove pending invitation record since it's now accepted
      try {
        await prisma.pendingInvitation.deleteMany({
          where: {
            senderId: invitationData.masterId,
            childEmail: invitationData.childEmail
          }
        });
      } catch (pendingInvitationError) {
        console.error('Error removing pending invitation:', pendingInvitationError);
        // Don't fail the request for this, just log it
      }

      console.log(`[${demoMode ? 'DEMO' : 'PROD'} MODE] Child user ${childUserData.email} successfully connected to master ${invitationData.masterEmail}`);

      return NextResponse.json({
        success: true,
        message: `Successfully connected child user to master${demoMode ? ' (Demo Mode)' : ''}`,
        childUser: {
          id: childUser.id,
          email: childUser.email,
          name: childUser.name,
          role: childUser.role
        },
        masterUser: {
          id: masterUser.id,
          email: masterUser.email,
          name: masterUser.name
        },
        demoMode
      });

    } catch (dbError) {
      console.error('Database error during invitation acceptance:', dbError);
      console.error('Database error details:', {
        message: dbError instanceof Error ? dbError.message : 'Unknown error',
        stack: dbError instanceof Error ? dbError.stack : undefined,
        masterId: invitationData?.masterId,
        childEmail: childUserData?.email
      });
      return NextResponse.json(
        { error: 'Database operation failed. Please try again.' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Unexpected error accepting invitation:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    return NextResponse.json(
      { error: 'Internal server error. Please try again.' },
      { status: 500 }
    );
  }
}
