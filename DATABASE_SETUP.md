# CopyTrade Database Setup with Prisma & Supabase

This document explains how to set up the database for the CopyTrade application using Prisma ORM with Supabase PostgreSQL.

## Overview

The database schema is designed to be highly scalable and supports:
- Master-Child user relationships
- Trading and order management
- Portfolio tracking
- Invitation system
- Notifications
- Audit logging
- Zerodha API integration

## Database Schema

### Core Tables

1. **users** - Master and child user accounts
2. **zerodha_credentials** - Zerodha API credentials and tokens
3. **master_child_relationships** - Many-to-many relationships between masters and children
4. **trades** - Trading transactions
5. **orders** - Order management
6. **portfolios** - Portfolio holdings
7. **trade_copies** - Trade copying history
8. **notifications** - User notifications
9. **audit_logs** - System audit trail

## Setup Instructions

### 1. Prerequisites

- Node.js 18+ installed
- Supabase account and project
- Zerodha Kite Connect API credentials

### 2. Environment Configuration

1. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

2. Update the `.env` file with your Supabase credentials:
   ```env
   DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres"
   DIRECT_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres"
   ```

3. Add other required environment variables as shown in `.env.example`

### 3. Install Dependencies

```bash
npm install
```

### 4. Database Migration

1. Generate Prisma client:
   ```bash
   npm run db:generate
   ```

2. Push the schema to your database:
   ```bash
   npm run db:push
   ```

   Or use migrations for production:
   ```bash
   npm run db:migrate
   ```

3. Seed the database with demo data:
   ```bash
   npm run db:seed
   ```

### 5. Verify Setup

1. Open Prisma Studio to view your data:
   ```bash
   npm run db:studio
   ```

2. Check that demo users and data were created successfully

## Database Services

The application includes comprehensive service classes for database operations:

### UserService
- User creation and authentication
- Zerodha credentials management
- User profile management

### RelationshipService
- Master-child relationship management
- Relationship statistics
- Bulk operations



### TradingService
- Trade execution and tracking
- Order management
- Trade copying to children

### PortfolioService
- Portfolio holdings management
- Real-time price updates
- P&L calculations

### NotificationService
- User notifications
- Bulk notification creation
- Notification statistics

### AuditService
- Comprehensive audit logging
- Security and compliance tracking
- Activity analytics

## Key Features

### Scalability Features

1. **Indexed Relationships**: All foreign keys are properly indexed
2. **Soft Deletes**: Important data uses soft deletes for data integrity
3. **Timestamps**: All tables include created/updated timestamps
4. **Unique Constraints**: Prevent duplicate relationships and data
5. **Decimal Precision**: Financial data uses proper decimal types

### Security Features

1. **Audit Logging**: All important actions are logged
2. **JWT Invitations**: Secure stateless invitation system
3. **Password Hashing**: bcrypt for password security
4. **JWT Authentication**: Secure session management

### Performance Features

1. **Database Indexes**: Optimized for common queries
2. **Connection Pooling**: Prisma handles connection pooling
3. **Query Optimization**: Efficient relationship loading
4. **Bulk Operations**: Support for bulk data operations

## Demo Mode Support

The schema supports both demo and production modes:
- `isDemo` flags on relevant tables
- Demo data seeding
- Separate demo user credentials
- Mock trading simulation

## Migration Strategy

For production deployments:

1. Use Prisma migrations instead of `db:push`
2. Run migrations in CI/CD pipeline
3. Backup database before migrations
4. Test migrations on staging environment

```bash
# Create a new migration
npx prisma migrate dev --name add_new_feature

# Deploy migrations to production
npx prisma migrate deploy
```

## Monitoring and Maintenance

### Regular Maintenance Tasks

1. **Cleanup Old Data**:
   ```typescript
   // Clean old notifications (30 days)
   await NotificationService.deleteOldNotifications(30)

   // Clean old audit logs (365 days)
   await AuditService.deleteOldLogs(365)
   ```

2. **Update Portfolio Prices**:
   ```typescript
   // Bulk update stock prices
   await PortfolioService.bulkUpdatePrices(priceUpdates)
   ```



### Database Monitoring

- Monitor connection pool usage
- Track slow queries
- Monitor storage usage
- Set up alerts for errors

## Troubleshooting

### Common Issues

1. **Connection Issues**:
   - Verify DATABASE_URL format
   - Check Supabase project status
   - Ensure IP allowlisting if required

2. **Migration Errors**:
   - Check for schema conflicts
   - Verify permissions
   - Review migration history

3. **Seed Errors**:
   - Ensure database is empty for initial seed
   - Check for unique constraint violations
   - Verify environment variables

### Support

For issues with the database setup:
1. Check Prisma documentation
2. Review Supabase logs
3. Check application logs
4. Verify environment configuration

## Next Steps

After setting up the database:
1. Update API routes to use Prisma services
2. Replace mock data with database calls
3. Implement real-time features
4. Set up monitoring and alerts
5. Configure backup strategies
