import { NextRequest, NextResponse } from 'next/server';
import { validateInvitationToken } from '@/lib/utils/jwt';

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { valid: false, error: 'Token is required' },
        { status: 400 }
      );
    }

    console.log('API validation endpoint called for token:', token.substring(0, 20) + '...');

    // Use the shared JWT validation utility
    const validationResult = validateInvitationToken(token);

    if (!validationResult.valid) {
      return NextResponse.json({
        valid: false,
        error: validationResult.error
      });
    }

    return NextResponse.json({
      valid: true,
      invitationData: validationResult.invitationData
    });

  } catch (error) {
    console.error('Token validation API error:', error);
    return NextResponse.json(
      { valid: false, error: 'Failed to validate token' },
      { status: 500 }
    );
  }
}
