import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { isDemoMode } from '@/app/config/demoMode';
import { ChildUserService } from '@/lib/services/childUserService';
import { UserService } from '@/lib/services/userService';
import { prisma } from '@/lib/prisma';

// Get list of connected child users
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user from database to get the internal user ID
    const dbUser = await UserService.findBySupabaseId(user.id);
    if (!dbUser) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    const demoMode = isDemoMode();

    // Get child users from database (works for both demo and production)
    const childUsers = await ChildUserService.getChildUsers(dbUser.id);

    // Get pending invitations from database
    const pendingInvitations = await prisma.pendingInvitation.findMany({
      where: {
        senderId: dbUser.id,
        expiresAt: {
          gt: new Date() // Only get non-expired invitations
        }
      },
      orderBy: {
        sentAt: 'desc'
      }
    });

    // Format pending invitations to match expected interface
    const formattedPendingInvitations = pendingInvitations.map(inv => ({
      id: inv.id,
      masterEmail: inv.senderEmail,
      childEmail: inv.childEmail,
      masterId: inv.senderId,
      token: '', // Don't expose JWT tokens in list
      status: 'pending' as const,
      sentAt: inv.sentAt.toISOString(),
      expiresAt: inv.expiresAt.toISOString()
    }));

    console.log(`[${demoMode ? 'DEMO' : 'PROD'} MODE] Child users for master ${dbUser.id}:`, {
      childCount: childUsers.length,
      pendingCount: formattedPendingInvitations.length
    });

    return NextResponse.json({
      success: true,
      childUsers,
      pendingInvitations: formattedPendingInvitations,
      demoMode
    });

  } catch (error) {
    console.error('Error fetching child users:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch child users',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
