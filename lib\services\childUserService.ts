import { prisma } from '@/lib/prisma';
import { User, MasterChildRelationship, UserRole } from '@prisma/client';

export interface ChildUserInfo {
  id: string;
  email: string;
  name: string;
  masterId: string;
  connectedAt: string;
  status: 'active' | 'pending' | 'inactive';
  zerodhaUserId?: string;
  lastActiveAt?: string;
}



export class ChildUserService {
  // Get child users for a master
  static async getChildUsers(masterId: string): Promise<ChildUserInfo[]> {
    const relationships = await prisma.masterChildRelationship.findMany({
      where: {
        masterId,
        isActive: true,
        deletedAt: null
      },
      include: {
        child: {
          include: {
            zerodhaCredentials: true
          }
        }
      },
      orderBy: {
        connectedAt: 'desc'
      }
    });

    return relationships.map(rel => ({
      id: rel.child.id,
      email: rel.child.email,
      name: rel.child.name || rel.child.email.split('@')[0],
      masterId: rel.masterId,
      connectedAt: rel.connectedAt.toISOString(),
      status: rel.child.isActive ? 'active' : 'inactive',
      zerodhaUserId: rel.child.zerodhaCredentials?.zerodhaUserId || undefined,
      lastActiveAt: rel.child.updatedAt.toISOString()
    }));
  }



  // Remove child user connection
  static async removeChildUser(childId: string, masterId: string): Promise<boolean> {
    try {
      const result = await prisma.masterChildRelationship.updateMany({
        where: {
          childId,
          masterId,
          isActive: true
        },
        data: {
          isActive: false,
          deletedAt: new Date()
        }
      });

      return result.count > 0;
    } catch (error) {
      console.error('Error removing child user:', error);
      return false;
    }
  }



  // Get child user count for a master
  static async getChildUserCount(masterId: string): Promise<number> {
    return prisma.masterChildRelationship.count({
      where: {
        masterId,
        isActive: true,
        deletedAt: null
      }
    });
  }

  // Check if user is a child of a specific master
  static async isChildOfMaster(childId: string, masterId: string): Promise<boolean> {
    const relationship = await prisma.masterChildRelationship.findFirst({
      where: {
        childId,
        masterId,
        isActive: true,
        deletedAt: null
      }
    });

    return relationship !== null;
  }

  // Get master for a child user
  static async getMasterForChild(childId: string): Promise<User | null> {
    const relationship = await prisma.masterChildRelationship.findFirst({
      where: {
        childId,
        isActive: true,
        deletedAt: null
      },
      include: {
        master: true
      }
    });

    return relationship?.master || null;
  }
}
