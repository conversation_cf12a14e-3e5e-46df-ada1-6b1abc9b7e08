import * as jwt from 'jsonwebtoken';

export interface InvitationTokenData {
  masterEmail: string;
  childEmail: string;
  masterId: string;
  type: string;
}

export interface JWTValidationResult {
  valid: boolean;
  invitationData?: InvitationTokenData;
  error?: string;
}

/**
 * Validates a JWT invitation token
 * @param token - The JWT token to validate
 * @returns Validation result with invitation data or error
 */
export function validateInvitationToken(token: string): JWTValidationResult {
  try {
    if (!token) {
      return { valid: false, error: 'Token is required' };
    }

    console.log('Validating JWT token:', token.substring(0, 20) + '...');
    console.log('JWT_SECRET available:', !!process.env.JWT_SECRET);
    console.log('Environment check:', {
      NODE_ENV: process.env.NODE_ENV,
      VERCEL_ENV: process.env.VERCEL_ENV,
      hasJWTSecret: !!process.env.JWT_SECRET,
      jwtSecretLength: process.env.JWT_SECRET?.length || 0
    });

    const JWT_SECRET = process.env.JWT_SECRET;
    if (!JWT_SECRET) {
      console.error('JWT_SECRET environment variable is not set');
      console.error('Available environment variables:', Object.keys(process.env).filter(key => key.includes('JWT') || key.includes('SECRET')));
      console.error('Environment info:', {
        NODE_ENV: process.env.NODE_ENV,
        VERCEL_ENV: process.env.VERCEL_ENV,
        platform: typeof window !== 'undefined' ? 'client' : 'server'
      });

      const errorMessage = process.env.NODE_ENV === 'production'
        ? 'Server configuration error: JWT secret not configured. Please contact support.'
        : 'JWT_SECRET environment variable is not set. Please check your .env.local file or deployment environment variables.';

      return { valid: false, error: errorMessage };
    }

    const decoded = jwt.verify(token, JWT_SECRET) as InvitationTokenData;

    if (!decoded || decoded.type !== 'invitation') {
      return { valid: false, error: 'Invalid token format' };
    }

    return {
      valid: true,
      invitationData: {
        masterEmail: decoded.masterEmail,
        childEmail: decoded.childEmail,
        masterId: decoded.masterId,
        type: decoded.type
      }
    };
  } catch (jwtError) {
    console.error('JWT token validation error:', jwtError);
    console.error('JWT_SECRET length:', process.env.JWT_SECRET?.length || 0);
    return { valid: false, error: 'Invalid or expired invitation token' };
  }
}

/**
 * Creates a JWT invitation token
 * @param data - The invitation data to encode
 * @param expiresIn - Token expiration time (default: 7 days)
 * @returns The JWT token or null if creation fails
 */
export function createInvitationToken(
  data: Omit<InvitationTokenData, 'type'>,
  expiresIn: string = '7d'
): string | null {
  try {
    const JWT_SECRET = process.env.JWT_SECRET;
    if (!JWT_SECRET) {
      console.error('JWT_SECRET environment variable is not set');
      return null;
    }

    const payload = {
      ...data,
      type: 'invitation',
    };

    const token = jwt.sign(payload, JWT_SECRET, { expiresIn } as any);

    console.log('JWT invitation token created successfully');
    return token;
  } catch (error) {
    console.error('Error creating JWT token:', error);
    return null;
  }
}

/**
 * Checks if JWT_SECRET is properly configured
 * @returns True if JWT_SECRET is available
 */
export function isJWTConfigured(): boolean {
  return !!process.env.JWT_SECRET;
}
