import { NextRequest, NextResponse } from 'next/server';
import { createInvitationToken, validateInvitationToken } from '@/lib/utils/jwt';

export async function GET(request: NextRequest) {
  try {
    // Only allow this in development or with a special debug key
    const debugKey = request.nextUrl.searchParams.get('debug_key');
    const isDev = process.env.NODE_ENV === 'development';
    const isValidDebugKey = debugKey === 'debug-jwt-test-2024';

    if (!isDev && !isValidDebugKey) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('JWT Debug Test Starting...');

    // Test JWT creation
    const testData = {
      masterEmail: '<EMAIL>',
      childEmail: '<EMAIL>',
      masterId: 'test-master-id'
    };

    console.log('Creating test JWT token...');
    const token = createInvitationToken(testData);

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Failed to create JWT token',
        jwtSecretAvailable: !!process.env.JWT_SECRET,
        jwtSecretLength: process.env.JWT_SECRET?.length || 0
      });
    }

    console.log('JWT token created successfully');

    // Test JWT validation
    console.log('Validating JWT token...');
    const validationResult = validateInvitationToken(token);

    return NextResponse.json({
      success: true,
      tokenCreated: !!token,
      tokenLength: token.length,
      tokenPreview: token.substring(0, 50) + '...',
      validationResult: {
        valid: validationResult.valid,
        error: validationResult.error,
        hasInvitationData: !!validationResult.invitationData
      },
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        VERCEL_ENV: process.env.VERCEL_ENV,
        hasJWTSecret: !!process.env.JWT_SECRET,
        jwtSecretLength: process.env.JWT_SECRET?.length || 0
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('JWT debug test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        VERCEL_ENV: process.env.VERCEL_ENV,
        hasJWTSecret: !!process.env.JWT_SECRET,
        jwtSecretLength: process.env.JWT_SECRET?.length || 0
      }
    }, { status: 500 });
  }
}
